import ServiceCard_2 from "@/Components/Card/ServiceCard_2";
import Faq from "@/Components/Faq/Faq";
import CardCarousel from "@/Components/FullStackDevelopment/CardCarousel";
import Process from "@/Components/FullStackDevelopment/process";
import Section1 from "@/Components/AI/Section1";
import Section2 from "@/Components/AI/Section2";
import Section4 from "@/Components/FullStackDevelopment/Section4";
import Section5 from "@/Components/FullStackDevelopment/Section5";
import Section6 from "@/Components/FullStackDevelopment/Section6";
import Section7 from "@/Components/FullStackDevelopment/Section7";
import Infocontainer from "@/Components/Infocontainer";
import Image from "next/image";
import React from "react";

const page = () => {
  const accordionData = [
    {
      title: "What is full stack development?",
      content: "A full stack development deals in both the front end and back-end sides of the development. The front end is the client side of the web app whereas the back end is the server side of the web app.",
    },
    {
      title: "What distinguishes generic web development from full stack web development?",
      content: "Full stack development typically results in speedier development times, much lower development costs overall, and significantly lower maintenance and testing expenses. However, generic web development might not be able to save money and develop quickly.",
    },
    {
      title: "How much time does it take to create an application using the Full Stack?",
      content: "The complexity, needs, and scope of a Full Stack application determine how long it takes to create. After analyzing your project's needs, Valueans creates a personalized timetable with development stages, checkpoints, and completion deadlines.",
    },
    {
      title: "Why should I choose full stack development services?",
      content: "Our full stack developers have practical knowledge of a variety of technologies. Choosing full stack development services expedites project development and improves user experiences by guaranteeing smooth end-to-end solutions. Our proficiency spans both front-end and back-end, yielding all-encompassing outcomes.",
    }
  ];
  const cardData = [
    {
      title: "Frontend Mastery",
      content:
        "We build engaging, responsive user interfaces using the latest technologies like React, Angular, and Vue.js, ensuring your users have an intuitive and dynamic experience across all devices.",
    },
    {
      title: "Backend Brilliance",
      content:
        "Our backend experts leverage Node.js, Python, Ruby on Rails, and other modern frameworks to create powerful server-side applications that prioritize data management, security, and performance.",
    },
    {
      title: "Database Management",
      content:
        "We provide robust database solutions, whether relational (like MySQL, PostgreSQL) or NoSQL (like MongoDB, Cassandra), ensuring that your data is securely stored and efficiently accessed.",
    },
    {
      title: "API Integration",
      content:
        "Our full stack services include efficient API integration, allowing your applications to communicate seamlessly with other platforms and services.",
    },
    {
      title: "Cloud Solutions",
      content:
        "As part of our full stack development services, we help businesses deploy & manage their applications in the cloud, using platforms like AWS, Azure, & Google Cloud, ensuring scalability, security, & cost-efficiency.",
    },
  ];

  return (
    <>
      <Section1
        backgroundImage={"/Images/AI-bg.jpeg"}
        heading={"Valueans Full Stack Development Services "}
        bannerText={"Frontend and backend excellence."}
      />
      <Infocontainer className={"mt-10"}>
        Valueans provides Full-Stack Development services covering all front-end
        and back-end software components. We create custom solutions from mobile
        app design and development to website design and development, and
        eCommerce solutions. We strive to offer solutions that are effective and
        bring higher returns for your business.
      </Infocontainer>
      <Section2
        lefttext={
          "Let’s Drive Business Growth and Success with Valueans AI Business Solutions"
        }
        righttext={
          "Turn your imagination into reality with our enterprise AI solutions by managing complex problems and huge data sets with great efficiency and ease. Valueans is here to empower your businesses through transformative AI solutions."
        }
      />
      <Section4 />
      <Section5 />
      <Section6 />
      <Section7 />
      <Faq content={accordionData}/>
    </>
  );
};

export default page;
