import React from "react";

const Section4 = () => {
  return (
    <div className="w-[85%] mx-auto mb-10 md:mb-24">
      <section className="flex flex-col md:flex-row justify-center md:justify-between gap-3 items-center bg-pink-100 p-4 md:py-6 md:px-[42px]">
        <div className="w-[90%] md:w-[40%] md:h-[500px] bg-purple-200 p-4 shadow-sm rounded-md">
          <h3 className="text-xl md:text-3xl font-semibold mb-1">
            Manual Testing
          </h3>
          <p className="text-base text-justify md:text-lg">
            Manual testing is a hands-on approach that captures nuances automation tools might miss, particularly for complex user interactions or areas where test automation is impractical. We identify bugs by using exploratory testing, acceptance testing, and real-world simulations to verify that the software meets user expectations. This comprehensive service covers the most popular software testing services: security, exploratory, ad-hoc, usability, localization, and installation. 

          </p>
        </div>
        <div className="w-[90%] md:w-[40%] md:h-[500px] p-4 bg-pink-200 shadow-sm rounded-md">
          <h3 className="text-xl md:text-3xl font-semibold mb-1">
            AI and ML-Driven Automation Testing
          </h3>
          <p className="text-base md:text-lg text-justify">
            Particularly for intricate user interactions or situations where test automation is impossible, manual testing is a hands-on method that captures subtleties that automated technologies can overlook. To make sure the program satisfies user expectations, we use acceptability testing, exploratory testing, and real-world simulations to find flaws. The most common software testing services are covered by this all-inclusive package, including compatibility, security, exploratory, ad-hoc, and usability.  
          </p>
        </div>
      </section>

      <section className="flex flex-col md:flex-row justify-center md:justify-between gap-3 items-center bg-purple-100 my-3 md:my-6 p-4 md:py-6 md:px-[42px]">
        <div className="w-[90%] md:w-[40%] md:h-[500px] bg-purple-200 p-4 shadow-sm rounded-md">
          <h3 className="text-xl md:text-3xl font-semibold mb-1">
            Regression Testing
          </h3>
          <p className="text-base md:text-lg text-justify">
            When a new code is added to an existing library, regression testing makes sure that changes to the code have not disrupted the existing functionality. Regression testing makes sure that product stability is maintained. New bugs are fixed and it is made sure that there are no ripple effects due to code changes. Regression testing creates an agile environment and automation helps release cycles in staying consistent. <a href='#' class='text-[#7716BC] hover:underline'>Low code automation</a> can be a big help here where possible. Using code that is tested and secure is a plus. 

          </p>
        </div>
        <div className="w-[90%] md:w-[40%] md:h-[500px] p-4 bg-pink-200 shadow-sm rounded-md">
          <h3 className="text-xl md:text-3xl font-semibold mb-1">
            API Testing
          </h3>
          <p className="text-base md:text-lg text-justify">
            The foundation of contemporary apps is made up of dependable APIs. Thorough API testing verifies that your endpoints are safe, refined, operational, and prepared to meet demands in the real world. To verify data flows, endpoint dependability, and security, we employ technologies such as Postman, REST Assured, and contract testing frameworks. Advanced API testing proactively detects vulnerabilities, tests performance under demand, and verifies compliance. 
          </p>
        </div>
      </section>

      <section className="flex flex-col md:flex-row justify-center md:justify-between gap-3 items-center bg-pink-100 my-3 md:my-6 p-4 md:py-6 md:px-[42px]">
        <div className="w-[90%] md:w-[40%] md:h-[500px] bg-purple-200 p-4 shadow-sm rounded-md">
          <h3 className="text-xl md:text-3xl font-semibold mb-1">
            Web and Mobile QA
          </h3>
          <p className="text-base md:text-lg text-justify">
            Your websites and applications should look fantastic and function flawlessly across all browsers and devices in a world where customers may switch between high-end desktop computers and mid-range smartphones. For a flawless user experience, we prioritize responsive design and <a href='#' class='text-[#7716BC] hover:underline'>cross-platform compatibility</a> in our online and mobile quality assurance service. In order to verify that your software is usable by those with impairments, we also carry out accessibility testing. 


          </p>
        </div>
        <div className="w-[90%] md:w-[40%] md:h-[500px] p-4 bg-pink-200 shadow-sm rounded-md">
          <h3 className="text-xl md:text-3xl font-semibold mb-1">
            Security Testing
          </h3>
          <p className="text-base md:text-lg text-justify">
            A new business experiences a data breach every day. Our security testing services examine vulnerabilities from the viewpoint of a hacker, delving deeply into your software's defenses. To proactively evaluate vulnerabilities, we employ industry-leading technologies like AI-driven security platforms. Our method identifies problems like SQL injection, cross-site scripting, and new attack routes by combining real-time anomaly detection, threat modeling, and vulnerability assessment. 


          </p>
        </div>
      </section>
      <section className="w-full md:w-[45%] mx-auto flex flex-col md:flex-row justify-center md:justify-between gap-3 items-center bg-purple-100 my-3 md:my-6 p-4 md:py-6 md:px-[42px]">
        <div className="w-[90%] mx-auto md:h-[500px] bg-purple-200 p-4 shadow-sm rounded-md">
          <h3 className="text-xl md:text-3xl font-semibold mb-1">
            Performance Testing
          </h3>
          <p className="text-base md:text-lg text-justify">
            We discover performance bottlenecks by simulating "rush hour" circumstances with our load and endurance testing. This implies that your application stays responsive and quick even during spikes in traffic. Stress testing exposes your software's weak areas by pushing it to the maximum. In addition to AI-driven load testing and predictive performance testing and analysis, we employ tools like JMeter, LoadRunner, and Gatling to simulate thousands of concurrent users.

          </p>
        </div>
      </section>
    </div>
  );
};

export default Section4;
