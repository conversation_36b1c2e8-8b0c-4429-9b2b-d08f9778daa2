'use client';

import React, { useState } from "react";
import Button from "../Buttons/Button";
import MultiStepForm from "./MultiStepForm";

const Section2 = ({
  paragraph1,
  paragraph2,
  paragraph3,
  heading,
  paragraph4,
  paragraph5,
}) => {
  const [showMultiStepForm, setShowMultiStepForm] = useState(false);

  const scrollToExpertForm = () => {
    // Find the form section and scroll to it
    const formSection = document.getElementById('expert-form-section');
    if (formSection) {
      formSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const toggleMultiStepForm = () => {
    setShowMultiStepForm(!showMultiStepForm);
  };

  return (
    <div className="w-[90%] mx-auto mb-10 md:my-24 mt-10">
      <div className="flex flex-col md:flex-row">
        <div className="md:w-[70%] space-y-6 text-justify">
          <p className="text-base md:text-lg">{paragraph1}</p>
          <p className="text-base md:text-lg">{paragraph2}</p>
          <p className="text-base md:text-lg">{paragraph3}</p>
          <div className="flex flex-col md:flex-row justify-around gap-4">
            <Button
              bgColor="bg-[#F245A1]"
              paddingX="px-4"
              onClick={scrollToExpertForm}
            >
              Estimate With an expert
            </Button>
            <Button
              bgColor="bg-[#F245A1]"
              paddingX="px-4"
              onClick={toggleMultiStepForm}
            >
              Use our Cost Estimator
            </Button>
          </div>
          <h1 className="text-xl md:text-3xl font-semibold">{heading}</h1>
          <p className="text-base md:text-lg"> {paragraph4}</p>
          <p className="text-base md:text-lg">{paragraph5}</p>
        </div>
        <div className="md:w-[30%]"></div>
      </div>

      {/* Multi-step form section */}
      <MultiStepForm isVisible={showMultiStepForm} />
    </div>
  );
};

export default Section2;
