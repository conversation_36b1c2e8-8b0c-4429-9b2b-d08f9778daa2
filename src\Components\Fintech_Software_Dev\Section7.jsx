import React from "react";

const Card = ({ title, description }) => {
  return (
    <div className="block w-full md:max-w-xl md:h-[140px] p-3 md:p-5 bg-white border  rounded-md shadow-md overflow-hidden">
      <h2 className="text-[#7716BC] text-base md:text-lg font-semibold mb-1 md:mb-2">
        {title}
      </h2>
      <p className="text-sm md:text-base text-justify">{description}</p>
    </div>
  );
};

const Section7 = () => {
  return (
    <div className="w-[85%] mx-auto mb-10 md:mb-24">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold capitalize">
        Why Valueans Deserves to Be Chosen Over Competitors in  the{" "}
        <span className="text-[#F245A1]">
          Development of Fintech Software and Services
        </span>
      </h2>
      <div className="flex flex-col justify-center items-center gap-3 md:gap-6 my-3 md:my-6">
        <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
          <Card
            title={"Increased Security and Thorough Regulations"}
            description={
              "Security comes at a premium with all fintech solutions. Through our software, safe transactions are facilitated while protecting data, all in compliance with global financial laws. "
            }
          />
          <Card
            title={" Flexibility and Scalability"}
            description={
              "Our solutions are with the future in mind, which is why they are perpetually scalable and flexible at changing market forces. "
            }
          />
        </div>

        <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
          <Card
            title={" Focus on Users"}
            description={
              "We emphasize customer satisfaction and interface enhancement regarding engagement. "
            }
          />
          <Card
            title={"Advanced Technologies"}
            description={
              "AI, blockchain, and big data are some of the cutting-edge technologies we employ while developing sophisticated fintech solutions to ensure business success"
            }
          />
        </div>
        <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
          
          <Card
            title={"Low Code Automation"}
            description={
              "Product development is accelerated with the use of existing libraries of code making use of intuitive drag and drop interfaces. Low code automation reduces the time to market. "
            }
          />
        </div>
      </div>
    </div>
  );
};

export default Section7;
