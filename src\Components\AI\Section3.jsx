import React from "react";

const Section3 = () => {
  return (
    <section className="w-[95%] md:w-[60%] mx-auto p-8 border border-[#F245A1] rounded-lg my-10 md:my-20">
      <h3 className="text-base md:text-xl font-semibold">
        With AI-Powered Solutions at Valueans, you can:
      </h3>
      <div className="py-5 flex flex-col text-sm md:text-base gap-3 md:gap-5">
        <div className="flex items-center gap-4">
          <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
          <p className="">Automate your business operation.</p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
          <p className="">Streamline business processes and workflows.</p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
          <p className="">Make informed decisions backed by useful data.</p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
          <p className="">
           Utilize <a href="#" className="text-[#7716BC] hover:underline">predictive analytics services</a> to enhance customer experiences.
          </p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
          <p className="">Boost your business agility.</p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 bg-[#F245A1]"></div>
          <p className="">
            Achieve a competitive edge by improving your products and services.
          </p>
        </div>
      </div>
    </section>
  );
};

export default Section3;
