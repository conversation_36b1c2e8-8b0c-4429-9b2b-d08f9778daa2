import React from "react";
import ServiceCount from "../Services/ServiceCount_2";

const Card = ({ title, description }) => {
  return (
    <div className="block w-full md:max-w-md h-auto md:h-[230px] p-4 bg-white border border-purple-500 rounded-md shadow">
      <h3 className="text-base font-semibold">{title}</h3>
      <p className="text-sm font-light">{description}</p>
    </div>
  );
};

const Section7 = () => {
  return (
    <section className="bg-pink-100 mb-10 md:mb-24 py-4 md:py-10">
      <div className="w-[85%] mx-auto">
        <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
          The <span className="text-[#7716BC]">SaaS Development</span> Life
          Cycle 
        </h2>
        <p className="w-full md:w-[80%] md:mx-auto text-base md:text-xl text-center">
          The development cycle of a Saas product is extremely crucial. All the
          steps must be carefully followed. It is directly proportional to the
          success of any application or product. 
        </p>
        <div className="flex flex-col justify-center items-center gap-3 md:gap-6 my-3 md:my-6">
          <div className="flex flex-col md:flex-row justify-center gap-3 md:gap-6">
            <div className="flex justify-center items-center gap-2">
              <ServiceCount>1</ServiceCount>
              <Card
                title={"Idea, Discovery, Requirements, and Planning"}
                description={
                  "Valueans start by understanding the customer. Before any application can begin we get an outlook of the customer, their company, aims, reasons, etc. It lays down a lot about the brand and its identity. Your targets will be jotted down. The competition will be analyzed and opportunities circled out. The stakeholders will be in on all of this progress and we will collaborate to form a detailed outline to move forward. "
                }
              />
            </div>
            <div className="flex justify-center items-center gap-2">
              <ServiceCount>2</ServiceCount>
              <Card 
                title={"Design and Development"}
                description={
                  "At Valueans, we divide the development process into two distinct sections; front-end development and back-end development. The front end includes the UI of any app/product using any of the countless technologies out there (eg. React, JS). During the front-end development, we also take care of the responsiveness of your app/product so that it may be viewable on a host of devices. Finally, we ensure faster loading speeds and lesser load time."
                }
              />
            </div>
          </div>
          <div className="flex flex-col md:flex-row justify-center gap-3 md:gap-6">
            <div className="flex justify-center items-center gap-2">
              <ServiceCount>3</ServiceCount>
              <Card
                title={"Cloud Infrastructure"}
                description={
                  "We set up your app/product on the cloud which could be Google Cloud, AWS, etc. Then we implement CI/CD so that the updates are regular and trustworthy to avoid any serious consequences in the future. After that, our team checks the performance metrics and makes necessary changes so you can get the best solution for your business. We distribute the traffic across various servers so that none of them crashes and avoid any problems in the future.."
                }
              />
            </div>
            <div className="flex justify-center items-center gap-2">
              <ServiceCount>4</ServiceCount>
              <Card
                title={"Testing and QA"}
                description={
                  "This includes testing all the functions and modules. Testing if the performance is up to scratch and fixing any bugs should they appear. The final product is deployed on cloud servers, allowing users to access it anytime. Valueans ensures a smooth onboarding process and provides comprehensive documentation to guide users effectively. Advanced automation tools guarantee a fast, efficient, and error-free deployment."
                }
              />
            </div>
          </div>
          <div className="flex justify-center">
            <div className="flex justify-center items-center gap-2">
              <ServiceCount>5</ServiceCount>
              <Card
                title={"Deployment, Launch, Ongoing Maintenance, and Updates"}
                description={
                  "Your app/product will be released to a select few people (you could call it a soft launch). Those reviews will help us adjust the product for the full launch. Don’t worry we will be with you post launch as well. Keep an eye on performance metrics, any bugs you might encounter, or any features that need adding to upgrade the systems. We feed off customer feedback, upgrading and providing technical assistance consistently. "
                }
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section7;
