import React from "react";
import Card from "./Card_2";
const Section4 = () => {
  const cardData = [
    {
      title: "Cloud-Managed Data Center Services",
      description:
        "We guarantee that your data is constantly accessible, stored safely, and handled with the highest care, from processing power to storage.",
    },
    {
      title: "Services For Cloud Infrastructure Services",
      description:
        "Regardless of architecture, we work with you to develop, build, and integrate current core computing, network, storage, and security services.",
    },
    {
      title: "Cloud Consulting",
      description:
        "Valueans' cloud consulting services help you reimagine your business growth and build a better future. Our team of experts guides you through the process from the initial call to the successful project delivery.",
    },
    {
      title: "Services For Cloud Operation",
      description:
        "To help you run and maximize your whole tech ecosystem in order to achieve your business objectives, we cover your whole cloud and on-premises tech stack. Our tech ecosystem includes third-party suppliers, apps, and platforms.",
    },
    {
      title: "Cloud Managed Network",
      description:
        "We manage every aspect of your network architecture across cloud environments, guaranteeing dependable, low latency, and seamless communication.",
    },
    {
      title: "Services For Hybrid Clouds",
      description:
        "We help you increase workload functionality and data access throughout your IT ecosystem by connecting your information systems, from on-premises core systems to private, public, and edge clouds.",
    },
  ];

  return (
    <div className="w-[85%] mx-auto my-10 md:my-24">
      <h2 className=" w-auto md:w-[80%] md:mx-auto text-xl md:text-3xl md:leading-[40px] text-center font-semibold capitalize mb-1 md:mb-3">
        Our <span className="text-[#F245A1]">managed cloud services</span> help{" "}
        you get high-Performance IT infrastructure
      </h2>
      <p className="w-auto md:w-[90%] md:mx-auto text-lg md:text-xl text-center capitalize">
        Our team of experts strives hard to deliver more value with greater
        agility to help you achieve better IT infrastructure through our cloud
        managed services.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 my-3 md:my-6">
        {cardData.map((card, index) => (
          <Card key={index} title={card.title} description={card.description} />
        ))}
      </div>
    </div>
  );
};

export default Section4;
