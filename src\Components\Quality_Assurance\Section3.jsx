import React from "react";

const Section3 = () => {
  return (
    <section className="bg-pink-100 mb-10 md:mb-24 p-4 md:p-8">
      <div className="w-[90%] mx-auto flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
        <div className="flex-1">
          <h2 className="text-2xl md:text-3xl md:leading-[40px] font-semibold">
            Streamline Your Testing with Valueans{" "}
            <span className="text-[#F245A1]">Software Testing Services</span>
          </h2>
          <p className="text-base md:text-xl text-justify">
            To make sure that the outcomes please everyone who uses your
            product, Valueans' QA team can test your software from the
            perspectives of stakeholders and end users. We guarantee that your
            product is safe, easy to use, devoid of bugs, and capable of
            managing heavy traffic volumes.
          </p>
        </div>
        <div className="flex-1">
          <section className="w-full  mx-auto p-8 border border-purple-800 rounded-2xl shadow-md">
            <h3 className="font-semibold text-base md:text-2xl ">
              With QA and Testing Services at Valueans, we:
            </h3>
            <div className="py-5 flex flex-col text-justify gap-3 md:gap-5">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 mt-2 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm  md:text-lg">
                  Offer top-notch manual testing services that make use of
                  customized testing scenarios and the replication of real-user
                  behavior.
                </p>
              </div>

              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 mt-2 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm text md:text-lg">
                  Test software from the very beginning of its development in
                  order to identify issues early.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 mt-2 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Execute With a range of actual devices, emulators, browser
                  stacks, clouds, and other platforms, manual software testing
                  is ideal for projects with little resources.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 mt-2 h-4 md:h-5 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Provide a variety of flexible cooperation models so you may
                  select the one that best fits your objectives, time
                  constraints, and financial constraints.
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </section>
  );
};

export default Section3;
