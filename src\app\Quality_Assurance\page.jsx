import Section1 from "@/Components/AI/Section1";
import Section2 from "@/Components/AI/Section2";
import Faq from "@/Components/Faq/Faq";
import Section3 from "@/Components/Quality_Assurance/Section3";
import Section4 from "@/Components/Quality_Assurance/Section4";
import Section5 from "@/Components/Quality_Assurance/Section5";
import Section6 from "@/Components/Quality_Assurance/Section6";
import Section7 from "@/Components/Quality_Assurance/Section7";
import Section8 from "@/Components/Quality_Assurance/Section8";

const page = () => {
  const accordionData = [
    {
      title: "What is software quality assurance testing?",
      content: "The process of keeping an eye on all software engineering procedures, activities, and techniques employed in a project to guarantee proper software quality and compliance with established standards is known as software quality assurance or SQA.",
    },
    {
      title: "Does manual testing need coding?",
      content: "A manual tester doesn't necessarily need to know how to create code. The ability to test functionality and comprehend how the application and server communicate is unaffected by this. Naturally, though, certain testing methods demand an understanding of design and code, which may be helpful when developing an improvement plan.",
    },
    {
      title: "Does QA include manual testing?",
      content: "Yes. Quality assurance, or QA for short, is a thorough procedure for confirming the code's functionality and sanity. It offers scripted automation testing as well as manual testing services",
    },
    {
      title: "Which is preferable, automated or manual testing?",
      content: "There is no one-size-fits-all solution because it always relies on the project's goals, budget, and time frame. The speed of the release and the number of features in the works will decide how best to balance automated and manual testing. Please feel free to get in touch with us for a consultation if you're unsure if you require automated or manual testing.",
    },
    {
      title: "What are QA services? What distinguishes them from software testing?",
      content: "Although they have different scopes, software testing and QA services are closely connected. The goals of both initiatives are to reduce errors and enhance the software development process. Software testing is only one aspect of quality assurance that is addressed by managed testing services, which provide a more thorough approach. Software testing concentrates on finding errors and preserving functionality, while QA services cover a broader range of procedures, such as risk management, to raise the software's overall quality.",
    }
  ];
  return (
    <div>
      <Section1
        backgroundImage={"/Images/QA-bg.jpeg"}
        heading={"Quality Assurance Services at Valueans"}
        bannerText={"Update your platform for a user and customer experience that is faster, simpler, and safer."}
      />
      <Section2 lefttext={"Quality Assurance and Testing Services"}
      righttext={"Make sure your goods and services live up to your expectations and your own. Our AI-led QA services provide quality, efficiency, and peace of mind since they are founded on our vast experience and expertise in QA and Valueans testing services."}/>
      <Section3 />
      <Section4 />
      <Section5 />
      <Section6 />
      <Section7 />
      <Section8 />
      <Faq content={accordionData}/>
    </div>
  );
};

export default page;
