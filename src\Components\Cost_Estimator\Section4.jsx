import React from "react";
import Button from "../Buttons/Button";
import Image from "next/image";

const Section4 = ({heading, paragrapgh,paragraph2, buttonBg}) => {
  return (
    <>
      <section className=" bg-[#7716BC] py-8">
        <div className="flex flex-col-reverse md:flex-row justify-center md:justify-between items-center w-[85vw] mx-auto ">
          <div className="md:w-[60%]">
            <h2 className="capitalize text-white text-2xl md:text-2xl leading-9 text-center md:text-left  font-extrabold">
              {heading}
            </h2>
            <p className="w-full md:w-2/3 text-base md:text-lg text-justify text-white font-normal m-1 ">
              {paragrapgh}
            </p>
            <p className="w-full md:w-2/3 text-base md:text-lg text-justify text-white font-normal m-1 mb-10">
              {paragraph2}
            </p>
            <div className="w-fit space-x-10 mx-auto md:mx-0">
              <Button bgColor="bg-[#F245A1]" paddingX="px-4">Estimate with our expert</Button>
              <Button bgColor="bg-[#F245A1]" paddingX="px-4">Use our cost Estimator</Button>
            </div>
          </div>
          <div className=" relative">
            <Image
              src="/Images/estimate.png"
              alt="estimate"
              className="flex-grow-0"
              width={400}
              height={400}
              objectFit="cover"
            />
          </div>
        </div>
      </section>
    </>
  );
};

export default Section4;
