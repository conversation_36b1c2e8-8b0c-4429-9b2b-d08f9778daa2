import React from "react";
import MaintenanceCard from "./Maintainance_Card";

const MaintenanceServices = () => {
  const bgColors = {
    pink: "bg-pink-100",
    purple: "bg-purple-100",
  };
  const maintenanceData = [
    {
      title: "SAP Support Services",
      description:
        "Experienced SAP professionals help you manage critical tasks effortlessly. Here's how:",
      containerBg: bgColors.pink,
      items: [
        "We make sure that your SAP solutions are delivered on time to meet your needs.",
        "We keep an eye on our fixes and continuously improve your process to keep everything running.",
        "We help you to get the most out of your SAP system, making sure it adds real value to your business.",
      ],
    },
    {
      title: "Web Maintenance Services",
      description:
        "Keeping your website in top shape is vital for user engagement and SEO performance. Our services include:",
      containerBg: bgColors.purple,
      items: [
        "We regularly check for vulnerabilities and make sure your site is safe from cyber threats.",
        "We create routine backups and have restore plans in place to prevent any data loss.",
        "We optimize your website's speed, so visitors enjoy a fast, seamless experience.",
      ],
    },
    {
      title: "Mobile App Maintenance Services",
      description:
        "We help your apps stay updated, secure, and engaging. Our maintenance and support services include:",
      containerBg: bgColors.pink,
      items: [
        "We identify and quickly resolve bugs unique to your app, making sure your apps perform flawlessly.",
        "We make sure your app works perfectly with the latest iOS and Android updates.",
        "We listen to your audience and ensure app features meet their needs and expectations.",
      ],
    },
    {
      title: "Content Management System CMS Support",
      description:
        "We ensure your CMS runs efficiently, so you can focus on creating great content. Our services include:",
      containerBg: bgColors.purple,
      items: [
        "We regularly update your plugins and themes to ensure your website remains secure and functions smoothly.",
        "We enhance your site load speed to quickly handle high traffic activities, giving users a great browsing experience.",
        "We make improvements on your site by analyzing user behavior and making them quickly to maximize conversions.",
      ],
    },
    {
      title: "Remote IT Support Services",
      description:
        "Our remote IT support ensures your business stays on track by offering:",
      containerBg: bgColors.pink,
      items: [
        "We provide 24/7 remote IT support to resolve any technical issues and get your systems back to running smoothly.",
        "We continuously monitor your systems and network to resolve potential problems before they occur.",
        "Our response team is around the clock to assist you and handle your IT challenges.",
      ],
    },
  ];
  return (
    <div className="grid grid-cols-1 gap-6">
      {maintenanceData.map((service, index) => (
        <MaintenanceCard
          key={index}
          title={service.title}
          description={service.description}
          items={service.items}
          containerBg={service.containerBg}
        />
      ))}
    </div>
  );
};

export default MaintenanceServices;
