import React from "react";

const BlueTopCard = ({ title, description,description2, PinkTopCardheight,cardHeaderHeight }) => {
  return (
    <div className="max-w-md relative ">
      <div className={`bg-blue-100 p-4 ${cardHeaderHeight? cardHeaderHeight : "h-[100px]"}  rounded-t-md absolute top-[-20px] w-full z-20 shadow-md text-center flex items-center justify-center`}>
        <h3 className="text-base md:text-lg font-semibold">{title}</h3>
      </div>

      <div
        className={`w-full border border-purple-500 p-1 rounded-md shadow-sm ${cardHeaderHeight? "pt-5" : "pt-16"} h-auto ${PinkTopCardheight ? PinkTopCardheight : ""}`}
      >
        <p className="text-sm md:text-base m-5 text-justify">{description}</p>
        <p className="text-sm md:text-base m-5 text-justify">{description2}</p>
      </div>
    </div>
  );
};

export default BlueTopCard;
