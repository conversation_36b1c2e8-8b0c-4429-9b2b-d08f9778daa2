"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/pagination";
import { Pagination } from "swiper/modules";
import Card from "./Card";

const dummyData = [
  {
    title: "Reduced Time to Market",
    description:
      "With AI enhancing automation and speeding up workflows, your business can launch new features and services faster. This gives you a competitive edge, enabling you to respond quickly to market changes and customer demands.",
  },
  {
    title: "Scalability and Flexibility",
    description:
      "ReOps enables you to scale your cloud infrastructure effortlessly as your business grows. AI ensures that resource allocation is optimized, making it easy to adjust to fluctuating workloads without manual oversight.",
  },
  {
    title: "Improved Security and Risk Management",
    description:
      "ReOps leverages AI for real-time monitoring and proactive risk management. This ensures that potential security threats, performance issues, or compliance violations are detected and addressed quickly, minimizing exposure and safeguarding your business.",
  },
  {
    title: "Increased Productivity",
    description:
      "By automating repetitive tasks and reducing manual intervention, ReOps frees up your development and operations teams to focus on high-value work. AI-powered tools like automated testing, code reviews, and performance optimization reduce the workload on human teams, increasing overall productivity.",
  },
];

const CardCarousel = () => {
  return (
    <Swiper 
      spaceBetween={20} 
      breakpoints={{
        640: { slidesPerView: 1 },
        768: { slidesPerView: 2 },
        1024: { slidesPerView: 3 }
      }}
      pagination={{ clickable: true }}
      modules={[Pagination]}
    >
      {dummyData.map((item, index) => (
        <SwiperSlide key={index}>
          <Card title={item.title} description={item.description} height={"h-auto md:h-[300px]"}/>
        </SwiperSlide>
      ))}
    </Swiper>
  );
};

export default CardCarousel;
