import React from "react";
import ServiceCount from "../Services/ServiceCount_2";
import ServiceLifecycleCard from "../Services/ServiceLifecycleCard_2";

const Process = () => {
  const discoveryAndResearch = [
    "Perform proper research and analysis.",
    "Gather requirements through different techniques like Questionnaires, User interviews, Surveys, etc.",
    "Create a mind map and customer journey maps.",
  ];

  const strategyAndPlanning = [
    "Create a detailed user persona.",
    "Construct concept development.",
    "Plan and strategize.",
    "Design a timeline according to the client's requirements.",
  ];

  const wireframing = [
    "Create low-fidelity skeletal wireframes.",
    "Ask for your feedback.",
  ];

  const visualDesign = [
    "Designers transform wireframes into high-fidelity designs, incorporating your brand's color scheme, typography, and visual elements.",
    "Develop comprehensive design systems and style guides.",
  ];

  const prototyping = [
    "Develop interactive prototypes that simulate the user experience.",
    "Ask for your feedback.",
  ];

  const developmentAndIntegration = [
    "Front-end and Back-end development.",
    "Integrate third-party services.",
    "Conduct usability testing to gather feedback from real users.",
    "Conduct iterative improvements.",
  ];

  const testing = [
    "QA team performs rigorous testing to make sure the design is pixel-perfect.",
    "Make sure the product performs consistently across different browsers and devices.",
    "Ask for your approval/feedback.",
  ];

  const implementationAndLaunching = [
    "After validating the product through testing, it is ready to be launched.",
    "Make sure everything is according to your expectations.",
    "Launch the final product.",
  ];

  const postLaunchSupportAndMaintenance = [
    "Address any bugs that might arise after the launch.",
    "Continuous monitoring and optimizing the performance.",
    "Collect user feedback and implement improvements based on user suggestions or requirements.",
  ];

  return (
    <div className="bg-pink-100 py-10 my-10 md:my-24">
      <div className="w-[90%] md:w-[85%] mx-auto">
        <h2 className="text-2xl md:text-[38px] md:leading-[57px] text-center">
        
          UI/UX <span className="text-[#7716BC]">Development Process</span>
        </h2>
      
        <div className="my-5 md:my-10">
          <div className="flex flex-col md:flex-row justify-center items-center gap-5">
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>1</ServiceCount>
              <ServiceLifecycleCard
                title="Discovery and Research"
                items={discoveryAndResearch}
              />
            </div>
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>2</ServiceCount>
              <ServiceLifecycleCard
                title="Strategy and Planning Act"
                items={strategyAndPlanning}
              />
            </div>
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>3</ServiceCount>
              <ServiceLifecycleCard
                title="Wireframing"
                items={wireframing}
              />
            </div>
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>4</ServiceCount>
              <ServiceLifecycleCard
                title="Visual Design"
                items={visualDesign}
              />
            </div>
          </div>
          <div className="flex flex-col md:flex-row justify-center items-center gap-5">
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>5</ServiceCount>
              <ServiceLifecycleCard
                title="Prototyping"
                items={prototyping}
              />
            </div>
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>6</ServiceCount>
              <ServiceLifecycleCard
                title="Development and Integration"
                items={developmentAndIntegration}
              />
            </div>
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>7</ServiceCount>
              <ServiceLifecycleCard
                title="Testing"
                items={testing}
              />
            </div>
          </div>
          <div className="flex flex-col md:flex-row justify-center items-center gap-5">
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>8</ServiceCount>
              <ServiceLifecycleCard
                title="Implementation and Launching"
                items={implementationAndLaunching}
              />
            </div>
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>9</ServiceCount>
              <ServiceLifecycleCard
                title="Post -Launch Support and Maintenance"
                items={postLaunchSupportAndMaintenance}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Process;
