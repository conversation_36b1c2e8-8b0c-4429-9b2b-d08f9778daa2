import Image from "next/image";

const Bulletpoint = ({ title, Content, bgColor = "bg-violet-100" }) => {
  return (
    <div
      className={`max-w-lg p-2 ${bgColor} h-[140px] border border-purple-500 rounded-md shadow-sm`}
    >
      <div className="flex items-start gap-1">
        <Image
          src={"/Images/pointer.png"}
          alt="pointer"
          width={32}
          height={32}
        />
        <h3 className="text-sm md:text-lg font-bold">{title}</h3>
      </div>

      <p className="text-sm md:text-base text-justify ml-8">{Content}</p>
    </div>
  );
};

const Section8 = () => {
  return (
    <div className="w-[85vw] mx-auto mb-10 md:mb-24">
      <h2 className="text-xl md:text-3xl md:leading-8 text-center capitalize font-semibold mb-2">
        The Process We Follow in Creating  Amazing Software for You
      </h2>
      <p className="w-full md:w-[65%] md:mx-auto text-base md:text-xl text-center capitalize">
        Requirements Analysis: At the beginning, we examine your project
        objectives, chronic pain areas, and identify the project goals that have
        been established. 
      </p>
      <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-4 mt-6 md:mt-[42px]">
        <Bulletpoint
          title={"UI/UX Design & Prototypes:"}
          Content={
            "At this stage, our team takes into consideration the complete workflow of the user and comes up with the GUI along with the cross-platform responsive experience. "
          }
        />
        <Bulletpoint
          title={"Development & Coding:"}
          Content={
            "Yours systems engineers develop multifunctional and fault tolerant software systems using the ultramodern technology in the market today."
          }
        />
      </div>
      <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-4 my-3 md:my-4">
        <Bulletpoint
          title={"Quality Assurance & Testing:"}
          Content={
            "We carry out diverse testing methods in order to make sure the software works as specified and meets all expectations. "
          }
        />
        <Bulletpoint
          title={"Deployment & Integration:"}
          Content={
            "We perform software installation and check system perimeter for the integration validation. "
          }
        />
      </div>
      <div className="flex  justify-center items-center gap-3 md:gap-4">
        <Bulletpoint
          title={"Ongoing Support & Optimization:"}
          Content={
            "We provide extensive support, maintenance and other changes with the aim of keeping the software up to date."
          }
        />
      </div>
    </div>
  );
};

export default Section8;
