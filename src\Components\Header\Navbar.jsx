"use client";
import React, { useState } from "react";
import { FaFacebook, FaInstagram, FaLinkedin } from "react-icons/fa";
import Image from "next/image";
import { ChevronDown, ChevronRight } from "lucide-react";
import { FaBars } from "react-icons/fa";
import Services from "./Services";
import Link from "next/link";
import Button from "../Buttons/Button";
import Technologies from "./Technologies";
import Industries from "./Industries";

const Navbar = () => {
  const [isServicesOpen, setIsServicesOpen] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false); // Toggle for mobile menu
  const [isTechnologiesOpen, setIsTechnologiesOpen] = useState(false);
  const [isIndustriesOpen, setIsIndustriesOpen] = useState(false);
  const [openAccordions, setOpenAccordions] = useState({
    software: false,
    itConsulting: false,
    testing: false,
    dataAnalytics: false,
    design: false,
    application: false,
  });

  const toggleAccordion = (key) => {
    setOpenAccordions((prev) => ({ ...prev, [key]: !prev[key] }));
  };

  return (
    <nav className="bg-white shadow-md">
      <div className="max-w-screen-md mx-auto py-1 md:py-4 px-2 md:px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between md:justify-center h-16">
          {/* Logo */}
          <Link href="/" className="flex-shrink-0">
            <Image
              src="/Images/logo.png"
              alt="Logo"
              width={65}
              height={42}
              className="w-[65px] h-[42px] md:w-[123px] md:h-[79px]"
            />
          </Link>

          {/* Desktop Menu */}
          <div className="hidden md:block">
            <div className="flex justify-center items-center">
              <div className="mx-10 flex items-center text-base space-x-1 relative">
                <Link
                  href="/"
                  className="font-poppins font-semibold   text-gray-900 hover:text-gray-600 px-3 py-2 rounded-md"
                >
                  Home
                </Link>
                {/* Services with Dropdown */}
                <div
                  className="relative z-50"
                  onMouseEnter={() => setIsServicesOpen(true)}
                  onMouseLeave={() => setIsServicesOpen(false)}
                >
                  <button className="flex items-center font-semibold text-gray-900 hover:text-gray-600 px-3 py-2 rounded-md">
                    Services
                    <ChevronDown
                      size={20}
                      className={`ml-2 transition-transform ${isServicesOpen ? "rotate-180" : "rotate-0"}`}
                    />
                  </button>
                  {isServicesOpen && (
                    <div className="absolute left-0 w-[900px] bg-white border border-gray-200 rounded-md shadow-lg">
                      <Services />
                    </div>
                  )}
                </div>
                <div
                  className="relative z-50"
                  onMouseEnter={() => setIsTechnologiesOpen(true)}
                  onMouseLeave={() => setIsTechnologiesOpen(false)}
                >
                  <button className="flex items-center font-poppins font-semibold text-gray-900 hover:text-gray-600 px-3 py-2 rounded-md">
                    Technologies
                    <ChevronDown
                      size={20}
                      className={`ml-2 transition-transform ${isTechnologiesOpen ? "rotate-180" : "rotate-0"}`}
                    />
                  </button>
                  {isTechnologiesOpen && (
                    <div className="absolute left-0 w-[800px] bg-white border border-gray-200 rounded-md shadow-lg">
                      {/* Add the content for your technologies section here */}
                      <Technologies />
                    </div>
                  )}
                </div>
                <div
                  className="relative z-50"
                  onMouseEnter={() => setIsIndustriesOpen(true)}
                  onMouseLeave={() => setIsIndustriesOpen(false)}
                >
                  <button className="flex items-center font-poppins font-semibold text-gray-900 hover:text-gray-600 px-3 py-2 rounded-md">
                    Industries
                    <ChevronDown
                      size={20}
                      className={`ml-2 transition-transform ${isIndustriesOpen ? "rotate-180" : "rotate-0"}`}
                    />
                  </button>
                  {isIndustriesOpen && (
                    <div className="absolute -left-3/4 w-[800px] bg-white border border-gray-200 rounded-md shadow-lg">
                      {/* Add the content for your technologies section here */}
                      <Industries />
                    </div>
                  )}
                </div>
                <Link
                  href="/Portfolio/web-app"
                  className="font-poppins font-semibold  text-gray-900 hover:text-gray-600 px-3 py-2 rounded-md"
                >
                  Portfolio
                </Link>
                <Link
                  href="/blog"
                  className="font-poppins font-semibold  text-gray-900 hover:text-gray-600 px-3 py-2 rounded-md"
                >
                  Blog
                </Link>
                <Link
                  href="#"
                  className="font-poppins font-semibold  text-gray-900 hover:text-gray-600 px-3 py-2 rounded-md"
                >
                  Contact
                </Link>
              </div>
              <Button to="/Cost_Estimator">Get an Estimate</Button>
              <div className="ml-2 pl-2 border-l-2 border-l-gray-800 flex justify-center items-center gap-6">
                <Link href="#">
                  <FaFacebook className="w-4 md:w-6 h-4 md:h-6 hover:scale-110 transition-transform" />
                </Link>
                <Link href="#">
                  <FaInstagram className="w-4 md:w-6 h-4 md:h-6 hover:scale-110 transition-transform" />
                </Link>
                <Link href="#">
                  <FaLinkedin className="w-4 md:w-6 h-4 md:h-6 hover:scale-110 transition-transform" />
                </Link>
              </div>
            </div>
          </div>

          {/* Mobile Menu Button */}
          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="flex items-center  text-black px-4 py-2 rounded-md"
            >
              <FaBars size={20} />
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white shadow-md">
          <div className="p-4 space-y-4">
            {/* Services Dropdown in Mobile */}
            <div>
              <button
                onClick={() => setIsServicesOpen(!isServicesOpen)}
                className="flex justify-between items-center font-poppins font-semibold text-[18px] leading-[27px] text-gray-900 w-full"
              >
                Services
                <ChevronDown
                  size={20}
                  className={`ml-2 transition-transform ${
                    isServicesOpen ? "rotate-180" : "rotate-0"
                  }`}
                />
              </button>
              {isServicesOpen && (
                <div className="mt-2 pl-4 space-y-4">
                  {/* Software Development Accordion */}
                  <div>
                    <button
                      onClick={() => toggleAccordion("software")}
                      className="flex justify-between items-center font-semibold text-gray-900 text-sm w-full"
                    >
                      Software Development
                      <ChevronDown
                        size={16}
                        className={`ml-2 transition-transform ${
                          openAccordions.software ? "rotate-180" : "rotate-0"
                        }`}
                      />
                    </button>
                    {openAccordions.software && (
                      <div className="pl-4 mt-2 space-y-1">
                        <Link
                          href="/Custom_Web_Development"
                          className="block text-gray-900 text-xs hover:bg-gray-200"
                        >
                          Custom Web Development
                        </Link>
                        <Link
                          href="/Saas"
                          className="block text-gray-900 text-xs hover:bg-gray-200"
                        >
                          Saas Development
                        </Link>
                        <Link
                          href="/Full_Stack_Development_Services"
                          className="block text-gray-900 text-xs hover:bg-gray-200"
                        >
                          Full Stack Development
                        </Link>
                        <Link
                          href="/Web-App-Development"
                          className="block text-gray-900 text-xs hover:bg-gray-200"
                        >
                          Web App Development
                        </Link>
                        <Link
                          href="/Fintech"
                          className="block text-gray-900 text-xs hover:bg-gray-200"
                        >
                          Fintech
                        </Link>
                        <Link
                          href="/Dedicated_Deployment_teams"
                          className="block text-gray-900 text-xs hover:bg-gray-200"
                        >
                          Dedicated Deployment Teams
                        </Link>
                        <Link
                          href="/HealthCare"
                          className="block text-gray-900 text-xs hover:bg-gray-200"
                        >
                          Healthcare Development
                        </Link>
                      </div>
                    )}
                  </div>

                  {/* IT Consulting Accordion */}
                  <div>
                    <button
                      onClick={() => toggleAccordion("itConsulting")}
                      className="flex justify-between items-center font-semibold text-gray-900 text-sm w-full"
                    >
                      IT Consulting
                      <ChevronDown
                        size={16}
                        className={`ml-2 transition-transform ${
                          openAccordions.itConsulting
                            ? "rotate-180"
                            : "rotate-0"
                        }`}
                      />
                    </button>
                    {openAccordions.itConsulting && (
                      <div className="pl-4 mt-2 space-y-1">
                        <Link
                          href="/Product_Management"
                          className="block text-gray-900 text-xs hover:bg-gray-200"
                        >
                          Product Management
                        </Link>
                      </div>
                    )}
                  </div>

                  {/* Testing & QA Accordion */}
                  <div>
                    <button
                      onClick={() => toggleAccordion("testing")}
                      className="flex justify-between items-center font-semibold text-gray-900 text-sm w-full"
                    >
                      Testing & QA
                      <ChevronDown
                        size={16}
                        className={`ml-2 transition-transform ${
                          openAccordions.testing ? "rotate-180" : "rotate-0"
                        }`}
                      />
                    </button>
                    {openAccordions.testing && (
                      <div className="pl-4 mt-2 space-y-1">
                        <Link
                          href="/Quality_Assurance"
                          className="block text-gray-900 text-xs hover:bg-gray-200"
                        >
                          Quality Assurance & Testing
                        </Link>
                      </div>
                    )}
                  </div>

                  {/* Data Analytics Accordion */}
                  <div>
                    <button
                      onClick={() => toggleAccordion("dataAnalytics")}
                      className="flex justify-between items-center font-semibold text-gray-900 text-sm w-full"
                    >
                      Data Analytics
                      <ChevronDown
                        size={16}
                        className={`ml-2 transition-transform ${
                          openAccordions.dataAnalytics
                            ? "rotate-180"
                            : "rotate-0"
                        }`}
                      />
                    </button>
                    {openAccordions.dataAnalytics && (
                      <div className="pl-4 mt-2 space-y-1">
                        <Link
                          href="/AI"
                          className="block text-gray-900 text-xs hover:bg-gray-200"
                        >
                          AI
                        </Link>
                        <Link
                          href="/ML"
                          className="block text-gray-900 text-xs hover:bg-gray-200"
                        >
                          ML
                        </Link>
                        <Link
                          href="/Business_Intelligence"
                          className="block text-gray-900 text-xs hover:bg-gray-200"
                        >
                          Business Intelligence
                        </Link>
                        <Link
                          href="/DataEngineering"
                          className="block text-gray-900 text-xs hover:bg-gray-200"
                        >
                          Data Engineering
                        </Link>
                        <Link
                          href="/Cloud_Services"
                          className="block text-gray-900 text-xs hover:bg-gray-200"
                        >
                          Cloud Services
                        </Link>
                      </div>
                    )}
                  </div>

                  {/* Design Services Accordion */}
                  <div>
                    <button
                      onClick={() => toggleAccordion("design")}
                      className="flex justify-between items-center font-semibold text-gray-900 text-sm w-full"
                    >
                      Design Services
                      <ChevronDown
                        size={16}
                        className={`ml-2 transition-transform ${
                          openAccordions.design ? "rotate-180" : "rotate-0"
                        }`}
                      />
                    </button>
                    {openAccordions.design && (
                      <div className="pl-4 mt-2 space-y-1">
                        <Link
                          href="/UI_UX"
                          className="block text-gray-900 text-xs hover:bg-gray-200"
                        >
                          UI/UX Designing
                        </Link>
                        <Link
                          href="/Mobile_First_Design"
                          className="block text-gray-900 text-xs hover:bg-gray-200"
                        >
                          Mobile First Design
                        </Link>
                      </div>
                    )}
                  </div>

                  {/* Application Services Accordion */}
                  <div>
                    <button
                      onClick={() => toggleAccordion("application")}
                      className="flex justify-between items-center font-semibold text-gray-900 text-sm w-full"
                    >
                      Application Services
                      <ChevronDown
                        size={16}
                        className={`ml-2 transition-transform ${
                          openAccordions.application ? "rotate-180" : "rotate-0"
                        }`}
                      />
                    </button>
                    {openAccordions.application && (
                      <div className="pl-4 mt-2 space-y-1">
                        <Link
                          href="/Mobile-App-development"
                          className="block text-gray-900 text-xs hover:bg-gray-200"
                        >
                          Mobile App Development
                        </Link>
                        <Link
                          href="/App_Integration"
                          className="block text-gray-900 text-xs hover:bg-gray-200"
                        >
                          Application Integration
                        </Link>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            <button
              onClick={() => setIsTechnologiesOpen(!isTechnologiesOpen)}
              className="flex justify-between items-center font-poppins font-semibold text-[18px] leading-[27px] text-gray-900 w-full"
            >
              Technologies
              <ChevronDown
                size={20}
                className={`ml-2 transition-transform ${
                  isTechnologiesOpen ? "rotate-180" : "rotate-0"
                }`}
              />
            </button>
            {isTechnologiesOpen && (
              <Technologies />
            )}
            <button
              onClick={() => setIsIndustriesOpen(!isIndustriesOpen)}
              className="flex justify-between items-center font-poppins font-semibold text-[18px] leading-[27px] text-gray-900 w-full"
            >
              Industries
              <ChevronDown
                size={20}
                className={`ml-2 transition-transform ${
                  isIndustriesOpen ? "rotate-180" : "rotate-0"
                }`}
              />
            </button>
            {isIndustriesOpen && (
              <Industries />
            )}
            <Link
              href="#"
              className="block font-poppins font-semibold text-[18px] leading-[27px] text-gray-900 hover:text-gray-600"
            >
              Portfolio
            </Link>
            <Link
              href="/blog"
              className="block font-poppins font-semibold text-[18px] leading-[27px] text-gray-900 hover:text-gray-600"
            >
              Blog
            </Link>
            <Link
              href="#"
              className="block font-poppins font-semibold text-[18px] leading-[27px] text-gray-900 hover:text-gray-600"
            >
              Contact
            </Link>
          </div>
          <div >
            <div className="max-w-fit mx-auto mb-3">
              <Button to="/Cost_Estimator">Get an Estimate</Button>
            </div>

            <div className="flex justify-center items-center gap-5 mb-2">
              <Link href="#">
                <FaFacebook className="w-6 h-6 hover:scale-110 transition-transform" />
              </Link>
              <Link href="#">
                <FaInstagram className="w-6 h-6 hover:scale-110 transition-transform" />
              </Link>
              <Link href="#">
                <FaLinkedin className="w-6 h-6 hover:scale-110 transition-transform" />
              </Link>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
