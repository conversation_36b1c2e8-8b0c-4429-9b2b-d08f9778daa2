import Section1 from "@/Components/AI/Section1";
import Section2 from "@/Components/AI/Section2";
import Card from "@/Components//App Integration/Card_2";
import Faq from "@/Components/Faq/UiUxFaq";
import HomeP8 from "@/Components/Homepage/HomeP8";
import CardCarousel from "@/Components/UI_UX/CardCarousel";
import Process from "@/Components/UI_UX/process";
import Image from "next/image";
import ServiceCard_2 from "@/Components/Card/ServiceCard_2";

const page = () => {
  const UiUxcardData = [
    {
      title: "User-Centered Designs",
      content:
        "Our UI/UX design services start and end with users. Our process involves conducting extensive user research to understand their needs, behaviors, and pain points. Our designs are intuitively functional and up-to-date. ",
    },
    {
      title: "Expert Team of Designers",
      content:
        "We have seasoned UI/UX designers with years of experience across various industries. Whether you belong to healthcare, travel, or fintech, our experts have designs that would suit your brand and industry. ",
    },
    {
      title: "Comprehensive Services",
      content:
        "We offer a full spectrum of UI/UX design services. Our end-to-end design services guarantee the highest quality product for you and your users. ",
    },
    {
      title: "Ongoing Support and Maintenance",
      content:
        "We won't abandon you after launching. We  <a href='' class='text-[#7716BC] hover:underline'> ongoing support and maintenance</a> to make sure that your product is compatible with the latest technologies and trends. ",
    },  
    {
      title: "Collaborative Process",
      content:
        "At Valueans, we have a collaborative nature while developing a product. We work closely with our developers in the experiment phase to ensure that design and development go hand in hand. We ask for your feedback at every step to implement necessary changes and enhancements.  ",
    },
  ];

  const UiUxTech = [
    {
      title: "Languages",
      content: ["HTML", "CSS", "Bootstrap", "jQuery"],
    },
    {
      title: "Wireframes & Prototypes",
      content: ["Balsamiq", "InVision", "Adobe XD", "Sketch"],
    },
    {
      title: "Visual Design",
      content: ["Figma", "Photoshop", "Illustrator", "InDesign"],
    },
  ];
  return (
    <div>
      <Section1
        backgroundImage={"Images/UiUx-bg.jpeg"}
        heading={"UI/UX Design Services"}
        bannerText={"Where aesthetics meets functionality"}
      />
      <Section2
        lefttext={
          "At Valueans, we specialize in developing customized IT solutions that empower businesses to harness the full potential of the cloud. Whether you're a startup or a global enterprise, our comprehensive suite of cloud services ensures your cloud infrastructure is optimized for performance, security, and scalability."
        }
        righttext={
          "If you're looking to optimize your existing cloud infrastructure, enhance your security posture, or migrate to a new cloud platform, Valueans is your go-to partner for all things cloud. Our cloud managed network solutions ensure your business stays connected, while our cloud managed data center services safeguard your valuable data."
        }
        fontClasses={"font-normal text-base md:text-lg"}
      />

      <section className="my-10 md:my-24 w-[85%] mx-auto">
        <h2 className="text-center text-2xl md:text-[38px] md:leading-[57px] mb-2 md:mb-5 ">
          Why Choose Us?
        </h2>
        <p className="text-center text-base md:text-xl capitalize mb-2 md:mb-5">
          There are hundreds of companies out there that offer the same UI/UX
          design services as us,
          <br /> so how are we different?{" "}
          <span className="font-bold">Keep reading:</span>
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6 p-6 ">
          {UiUxcardData.slice(0, 3).map((card, index) => (
            <ServiceCard_2
              key={index}
              title={card.title}
              content={card.content}
            />
          ))}

          {/* Conditionally render the last two cards centered */}
          {UiUxcardData.length > 3 && (
            <div className="col-span-3 flex justify-center gap-6">
              <ServiceCard_2
                key={3}
                title={UiUxcardData[3].title}
                content={UiUxcardData[3].content}
              />
              <ServiceCard_2
                key={4}
                title={UiUxcardData[4].title}
                content={UiUxcardData[4].content}
              />
            </div>
          )}
        </div>
      </section>
      <HomeP8
        heading={"Start a conversation with us today"}
        paragrapgh={
          "Interested to know more about our UI/UX development services but confused about how to get started? Contact us now and we’ll help you to understand what’s best for your business. Our team is ready to be your partner throughout the process. "
        }
      />
      <section className="mt-10 md:mt-24 ">
        <h2 className="text-2xl md:text-[38px] md:leading-[57px] text-center mb-4 md:mb-10">
          What We <span className="text-[#F245A1]">Offer</span>
        </h2>

        <CardCarousel />
      </section>
      <section className="my-10 md:my-24 w-[85%] mx-auto">
        <h2 className="text-2xl md:text-[38px] md:leading-[57px] font-semibold text-center">
          UI/UX Technology at <span className="text-[#F245A1]">Valueans</span>
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 mt-5 gap-6">
          {UiUxTech.map((item, index) => (
            <div key={index} className="">
              <div className="justify-items-center">
                <Card title={item.title} content={item.content} />
              </div>
            </div>
          ))}
        </div>
      </section>

      <section className="py-5 md:py-10 my-10 md:my-24 bg-blue-100">
        <div className="w-[85%] mx-auto flex flex-col md:flex-row justify-between items-center">
          <div className="flex flex-col gap-10">
            <div className="block w-full  p-6 bg-pink-100 border border-purple-500 rounded-lg shadow overflow-hidden">
              <h3 className="text-[#7716BC] text-base md:text-2xl font-semibold">
                Challenges Our UI/UX Designs Can Handle
              </h3>
              <ul>
                <li>Complex user journeys</li>
                <li>Cross-platform inconsistency</li>
                <li>Insufficient Productivityr</li>
                <li>Outdated design solutions</li>
                <li> Low user-engagement Usability Issues</li>
              </ul>
            </div>
            <div className="block w-full  p-6 bg-purple-100 border border-purple-500 rounded-lg shadow overflow-hidden">
              <h3 className="text-[#7716BC] text-base md:text-2xl font-semibold">
                Features of Our UI/UX Design Services
              </h3>
              <ul>
                <li>AR Experience Design</li>
                <li>Custom Iconography and visual elements</li>
                <li>Mobile app UI and UX design services</li>
                <li>Responsive designs</li>
                <li>UI and UX consulting</li>
                <li>Data-driven designs</li>
                <li>Design workshops</li>
                <li>Web design services</li>
              </ul>
            </div>
          </div>
          <div>
            <Image
              src={"/Images/uiux_frame.png"}
              alt="UI Ux"
              width={500}
              height={500}
            />
          </div>
        </div>
      </section>
      <Process />
      <Faq />
    </div>
  );
};

export default page;
