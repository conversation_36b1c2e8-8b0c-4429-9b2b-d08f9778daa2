import React from "react";
import InfoCard from "./InfoCard";

const Section4 = () => {
  const services = [
    {
      title: "AI Consulting",
      description:
        "Our AI Consulting services help you achieve your business goals starting from the first meeting where we discuss and create a tailored AI roadmap. Then we develop a thorough and impactful strategy that helps avoid pitfalls and get the most out of Artificial Intelligence.",
    },
    {
      title: "MLOps",
      description:
        'We have a decade of experience in providing digital solutions, so we streamline the entire MLOps lifecycle for your business. Our <a href="" class="text-[#7716BC] hover:underline">machine learning operations (MLOps)</a> services aim to improve your customer experiences, better return on investment, and quicker development times.',
    },
    {
      title: "Optimization Engines",
      description:
        'Our Optimization Engines provide your business with next-generation solutions throughout the vehicle lifecycle. We improve your system design and foster continuous performance to deliver the best solution. <a href="" class="text-[#7716BC] hover:underline">We deal with the most critical AI shortcomings that are the biggest obstacle to your progress.</a>',
    },
    {
      title: "Generative AI Consulting Services",
      description:
        "With our Generative AI for software development services, you can easily adopt AI for your business and innovate it. Our enterprise AI solutions help you meet the rising business challenges of being available anywhere, anytime. We take a responsible approach to AI and effectively custom LLMs for your enterprise.",
    },
    {
      title: "Responsible AI",
      description:
        "With the emergence of AI in various fields, it has become a serious threat for businesses to protect their company’s and customers’ data. We build ethical products and services with a strong focus on a responsible approach to AI solutions development and application of responsible AI framework.",
    },
    {
      title: "Forecasting Systems",
      description:
        "Valueans helps you make prompt and accurate business forecasts powered by AI so you can make quick and informed decisions for your business. We offer a dedicated strategy for best forecasting and adopting our forecasting solutions will help you stay at the top of your business game.",
    },
  ];

  return (
    <section className="w-[90%] mx-auto my-10 md:my-24">
      <div className="  px-4">
        <h2 className="text-xl md:text-3xl text-center font-semibold mb-8 ">
          Our <span className="text-[#F245A1]">AI Business Solutions</span> Help
          You Make Better Decisions For Faster Growth.
        </h2>
        <p className="text-base md:text-xl text-center">
          Let’s explore how we create better ways to transform your
          business with our AI-applied solutions.
        </p>
        <div className="grid grid-cols-1 mt-5 justify-items-center md:grid-cols-2 gap-y-6 gap-x-12 md:gap-y-12 md:gap-x-16">
          {services.map((service, index) => (
            <InfoCard
              key={index}
              title={service.title}
              description={service.description}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Section4;
