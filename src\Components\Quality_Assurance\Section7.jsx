import Card from "@/Components/App Integration/Card";

// const Card_1 = ({ title, description }) => {
//   return (
//     <div className="block w-full md:max-w-sm h-auto md:h-[382px] p-4 md:py-8 bg-white border border-purple-500 rounded-md shadow-sm">
//       <h3 className="text-lg md:text-2xl font-semibold mb-1 md:mb-4">
//         {title}
//       </h3>
//       <p className="text-base md:text-xl">{description}</p>
//     </div>
//   );
// };
// const Card_2 = ({ title, description }) => {
//   return (
//     <div className="block w-full md:max-w-sm h-auto md:h-[316px] p-4 md:py-8 bg-white border border-purple-500 rounded-md shadow-sm">
//       <h3 className="text-lg md:text-2xl font-semibold mb-1 md:mb-4">
//         {title}
//       </h3>
//       <p className="text-base md:text-xl">{description}</p>
//     </div>
//   );
// };

const Section7 = () => {
  return (
    <div className="w-[85%] mx-auto mb-10 md:mb-24">
      <h2 className="text-2xl md:text-[38px] md:leading-[57px] text-center font-semibold">
        <span className="text-[#F245A1]">Valueans</span> Covers a Wide Range of
        Industries
      </h2>
      <div className="flex flex-col md:flex-row justify-center items-center gap-4 my-3 md:my-6">
        <Card
          title={"E-commerce and Retail"}
          description={
            "Our staff assists you in improving your <a href='#' class='text-[#7716BC] hover:underline'>supply chain management</a> as well as the <a href='#' class='text-[#7716BC] hover:underline'>customer journey optimization</a>, from the moment they interact with your goods to the point at which they take the desired action. "
          }
          height={"md:h-[500px]"}
          border={"border-purple-500"}
        />
        <Card
          title={"Financial Services"}
          description={
            "We support the seamless operation of the connected systems, the complete functioning of your <a href='#' class='text-[#7716BC] hover:underline'>financial app</a> or system for all potential users, and the complete protection of their data."
          }
          height={"md:h-[500px]"}
          border={"border-purple-500"}
        />
        <Card
          title={"Software & Hi-Tech"}
          description={
            "Manual software testing services are essential to releasing a fiercely competitive product that satisfies user requests while meeting the high levels of quality that the market requires today. "
          }
          height={"md:h-[500px]"}
          border={"border-purple-500"}
        />
        <Card
          title={"Autonomous Driving & Logistics"}
          description={
            "With Valueans experience in manual testing, <a href='#' class='text-[#7716BC] hover:underline'>transport management services</a>, vessel schedule, tracking systems, and cost management may all be made even more efficient and intuitive. We constantly work on next generation solutions such as <a href='#' class='text-[#7716BC] hover:underline'>autonomous driving</a>, trying to create safe and smart transport solutions.  "
          }
          height={"md:h-[500px]"}
          border={"border-purple-500"}
        />
      </div>
      <div className="w-full md:w-[80%] md:mx-auto flex flex-col md:flex-row justify-center items-center gap-4">
        <Card
          title={"Entertainment & Media"}
          description={
            "Before releasing your product, use functional, usability, or performance testing to address any issues. Consequently, you will be able to promote an excellent product that will get remarkable results.  "
          }
          height={"md:h-[500px]"}
          border={"border-purple-500"}
        />
        <Card
          title={"Medical & Life Sciences"}
          description={
            "Benefit from our software testing and elevate your brand with cutting-edge functionality, visually appealing user interface elements, loyalty plans, and tailored suggestions. "
          }
          height={"md:h-[500px]"}
          border={"border-purple-500"}
        />
        <Card
          title={"Social Media"}
          description={
            "Using the newest tools and methods, improve the quality of social media forums, blogs, image-sharing websites, social networks, and customer review sites."
          }
          height={"md:h-[500px]"}
          border={"border-purple-500"}
        />
      </div>
    </div>
  );
};

export default Section7;
