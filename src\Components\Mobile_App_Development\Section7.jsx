import React from "react";

const Container = ({ children, bgColor = "bg-white", className = "" }) => {
  return (
    <div className={`w-full p-3 md:px-[42px] md:py-6 ${bgColor} ${className}`}>
      {children}
    </div>
  );
};
const Card = ({ title, description, bgColor = "bg-white" }) => {
  return (
    <div
      className={`block max-w-sm p-3 md:p-4 shadow-sm rounded-md ${bgColor}`}
    >
      <h3 className="text-lg md:text-xl font-semibold capitalize">
        {title}
      </h3>
      <p className="text-base md:text-lg text-justify">{description}</p>
    </div>
  );
};

const Section7 = () => {
  return (
    <div className="w-[85vw] mx-auto mb-10 md:mb-24">
      <h2 className="text-xl md:text-3xl text-center font-semibold">
         <span className="text-[#F245A1]">Custom Mobile App Development </span>Services for Various Platforms
      </h2>
      <div className="w-full md:w-[85%] md:mx-auto mt-6 md:mt-[42px]">
        <Container
          className="flex flex-col md:flex-row justify-center md:justify-between items-center gap-3 p-3 md:p-6"
          bgColor="bg-pink-100"
        >
          <Card
            bgColor="bg-purple-200"
            title={"iOS App Development"}
            description={
              "Are you prepared to develop your concepts into complete, reliable, and expandable iOS mobile applications? Our seamless interaction with the Apple ecosystem guarantees a faultless experience for your users. We develop iOS apps that surpass user expectations while also fulfilling your business objectives by utilizing the newest technology and customized features."
            }
          />
          <Card
            bgColor="bg-pink-200"
            title={"Android App Development"}
            description={
              "On every contemporary Android smartphone, our team creates Android apps that highlight your company. Make robust, intuitive apps that are suited to your needs. Our Android app development services, from the first idea to the last release, guarantee that your app stands out in a crowded market and improves your customers' experiences. "
            }
          />
        </Container>
        <Container
          className="flex flex-col md:flex-row justify-center md:justify-between items-center  gap-3 p-3 md:p-6 my-3 md:my-6"
          bgColor="bg-violet-100"
        >
          <Card
            bgColor="bg-violet-200"
            title={"Wearables and Embedded Software"}
            description={
              "Developing companion apps is essential in today's fast-paced world. Depending on your demands, we design a wide range of wearable devices that smoothly interact with smart gadgets or custom peripherals. From fitness trackers to smartwatches and other connected gadgets, accelerate functionality and enhance the user experience, making your users even more committed to your brand. "
            }
          />
          <Card
            bgColor="bg-pink-200"
            title={"Native Applications for Mobile Devices"}
            description={
              "For an individual seeking adaptability, our group creates stable native applications for both iOS and Android. Our main goal is to match app development services to your security and business needs. Utilizing platform-specific features, we develop safe, scalable, and user-friendly mobile experiences that expand your company's reach and engage your end users."
            }
          />
        </Container>
        <Container
          className="flex flex-col md:flex-row justify-center md:justify-between items-center  gap-3 p-3 md:p-6"
          bgColor="bg-pink-100"
        >
          <Card
            bgColor="bg-purple-100"
            title={"Cross-Platform Apps"}
            description={
              "Cross-platform applications are an excellent addition to your company because they are affordable and simple to maintain. They combine the best features of web apps and native apps, and they function flawlessly in a variety of environments. Using this method, we develop mobile applications that offer consistent user experience across all devices and faultless performance.  "
            }
          />
          <Card
            bgColor="bg-pink-200"
            title={"Progressive Web Apps"}
            description={
              "By combining native-like functionality with simple install-ability, our Progressive Web App (PWA) development services give you the best of both worlds. By combining the benefits of online and mobile apps, PWAs are quick, dependable, and captivating. Regardless of the device your audience is using, you can use our skills to create a fantastic digital experience."
            }
          />
        </Container>
      </div>
    </div>
  );
};

export default Section7;
