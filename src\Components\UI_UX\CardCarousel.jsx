"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/pagination";
import { Pagination } from "swiper/modules";
import Card from "./Gradient_card";

const cardData = [
  {
    title: "Prototyping and Wireframing",
    description:
      "Our team carefully tests the usability of the final product before deploying it using fully interactive prototypes, data visualization, and interface designs. We design low-fidelity and high-fidelity wireframes to outline the layout and functionality of each screen.",
    items: [],
  },
  {
    title: "UI/UX for Native Apps",
    description:
      "Our deep understanding of native mobile user interfaces and experiences, along with our proficiency in both iOS and Android app development, allows us to provide exceptional UI/UX development services especially mobile UI/UX design services that are tailored to your specific requirements.",
    items: [],
  },
  {
    title: "Visual Design and Branding",
    description:
      "Our visual design services focus on creating aesthetically pleasing and brand-consistent interfaces. We make sure that every design element aligns with your brand identity and appeals to your target audience. This includes:",
    items: [
      "Interface Designs",
      "Animated UI Designs",
      "Custom Icon Set Designs",
      "NFT Designs",
      "Design systems",
    ],
  },
  {
    title: "Enterprise Software Design",
    description:
      "Clear, flexible, and consistent enterprise system UI/UX can help you meet your target user needs, whether they’re related to increasing consumer interaction or modernizing business procedures.",
    items: [
     
    ],
  },
  // Add more cards here if needed
];

const CardCarousel = () => {
  return (
    <Swiper
      spaceBetween={20}
      breakpoints={{
        640: { slidesPerView: 1 },
        768: { slidesPerView: 2 },
        1024: { slidesPerView: 3 },
      }}
      pagination={{ clickable: true }}
      modules={[Pagination]}
    >
      {cardData.map((item, index) => (
        <SwiperSlide key={index}>
          <Card
            title={item.title}
            description={item.description}
            items={item.items}
            height={"h-[400px]"}
          />
        </SwiperSlide>
      ))}
    </Swiper>
  );
};

export default CardCarousel;
