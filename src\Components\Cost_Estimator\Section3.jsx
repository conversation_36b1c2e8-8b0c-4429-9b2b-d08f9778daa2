import React from "react";
import PinkTopCard from "../PWA_development/PinkTopCard";
import BlueTopCard from "../Industries/E-commerce/BlueTopCard";

const Section3 = ({
  heading,
  paragraph,
  card1Title,
  card1Description1,
  card1Description2,
  card2Title,
  card2Description1,
  card2Description2,
  card3Title,
  card3Description1,
  card3Description2,
  card4Title,
  card4Description1,
  card4Description2,
}) => {
  return (
    <div className="w-[90%] mx-auto mb-10 md:my-24 mt-10">
      <div className="flex">
        <div className="md:w-[50%]">
          <h1 className=" text-xl md:text-3xl font-semibold">{heading}</h1>
          <p>{paragraph}</p>
        </div>
        <div className="md:w-[50%] flex flex-col space-y-10 h-[500px] overflow-y-auto">
          <div className="mt-10">
            <PinkTopCard
              cardHeaderHeight={"h-[50px]"}
              title={card1Title}
              description={card1Description1}
              description2={card1Description2}
            />
          </div>
          <div className=" flex justify-end">
            <BlueTopCard
              cardHeaderHeight={"h-[50px]"}
              title={card2Title}
              description={card2Description1}
              description2={card2Description2}
            />
          </div>
          <div>
            <PinkTopCard
              cardHeaderHeight={"h-[50px]"}
              title={card3Title}
              description={card3Description1}
              description2={card3Description2}
            />
          </div>
          <div className=" flex justify-end">
            <BlueTopCard
              cardHeaderHeight={"h-[50px]"}
              title={card4Title}
              description={card4Description1}
              description2={card4Description2}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Section3;
