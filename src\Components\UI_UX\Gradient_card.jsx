import React from 'react'

const Gradient_card = ({ title, description, items, height  }) => {
  return (
    <>
        <div className={`block w-[90vw] ${height? height:"h-auto"} md:max-w-[300px]    mx-auto bg-gradient-to-br from-pink-500 to-purple-700 text-white p-6 rounded-lg`}>
      <h2 className="text-xl font-bold">{title}</h2>
      <p className="mt-2 text-base text-justify">{description}</p>
      <ul className="mt-3 space-y-1">
        {items.map((item, index) => (
          <li key={index} className="font-semibold text-base text-justify">• {item}</li>
        ))}
      </ul>
    </div>
    </>
  )
}

export default Gradient_card
