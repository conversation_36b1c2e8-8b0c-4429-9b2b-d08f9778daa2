const Card_1 = ({ title, description }) => {
  return (
    <div className="block w-[380px] h-auto md:h-[470px] bg-white border border-gray-200 shadow-lg rounded-lg p-4 ">
      <h3 className="text-[#7716BC] text-base md:text-lg font-semibold mb-3 md:mb-4">
        {title}
      </h3>
      <p className="text-sm md:text-base text-justify">{description}</p>
    </div>
  );
};


const Section3 = () => {
  return (
    <div className="w-[85vw] mx-auto mb-10 md:mb-24">
      <h2 className="text-xl md:text-3xl md:leading-10 text-center font-semibold">
        Our{" "}
        <span className="text-[#F245A1]">
          Custom Software Development
        </span>{" "}
        Services
      </h2>
      <div className="flex flex-col justify-center items-center gap-4 md:gap-[18px] mt-6 md:mt-[42px]">
        <div className="flex flex-col md:flex-row justify-center items-center gap-4 md:gap-[18px]">
          <Card_1
            title={"B2B Custom Software Development"}
            description={
              "We design and develop B2B custom software at the application level to improve operational efficiency, workflow, and performance of the company. From managing digital transformation at a startup to enterprise level solutions, we ensure that there is seamless integration. Our B2B Custom Software services focus on improving collaboration, resource allocation, and customer experience. We cover the entire process from requirement analysis, through UI/UX designing, development, testing, and deployment."
            }
          />
          <Card_1
            title={"Customized Software Solutions"}
            description={
              "Our company seeks to design and build tailored software solutions that best address your unique business challenges. Be it custom software development, ERP systems, or AI solutions, we make sure our products evolve as you scale. Every company is unique and that is why we ensure our products offer specific solutions to specific problems. We try to build systems that can allow for future growth and changes in the business environment."
            }
          />
          <Card_1
            title={"Enterprise Software Development"}
            description={
              "We provide specialized services in enterprise software development, offering business-grade applications that can support entire organizations. Our business solutions automate tasks, foster teamwork and facilitate smarter decision making through the use of analytic insights. We apply high-end solutions, such as cloud computing, blocking, and AI, to ensure your organization remains competitive in the market. We develop and implement enterprise grade CRM systems, ERP systems, and other applications such as inventory and human resources management systems to simplify your processes."
            }
          />
        </div>
        <div className="flex flex-col md:flex-row justify-center items-center gap-4 md:gap-[18px]">
          <Card_1
            title={"Custom Software Application Development"}
            description={
              "If you require a specific application, we can help you with that. We develop custom applications that fully utilize your company's existing infrastructure, reducing the chances of problems across the various departments. We develop web, mobile, and desktop applications that can be used to improve organizational productivity while fostering customer interactions. Our applications are designed for ease of use while at the same time providing advanced features that are tailored for your business."
            }
          />
          <Card_1
            title={"System Modernization"}
            description={
              "Increased inefficiency of a business can be a byproduct of obsolete software. We assist in upgrading legacy systems and enhancing security alongside boosting performance through technological advancements. Updating these services includes reengineering older applications as well as migrating them to the cloud while adjusting security standards. This promotes improved overall efficiency alongside reduced maintenance costs."
            }
          />
          <Card_1
            title={"Cloud Software Development"}
            description={
              "We provide customized software solutions which incorporate cloud technology in order to enhance security, access, and scalability. Our cloud solutions allow for simple integration with other business applications and enable real-time collaboration. Companies can utilize our software as a service or an infrastructure platform to encourage Paas or IaS, further improving digital transformation."
            }
          />
        </div>
        <div className="flex flex-col md:flex-row justify-center items-center gap-4 md:gap-[18px]">
          <Card_1
            title={"Integration of AI and ML"}
            description={
              "Alongside business automation and insightful analysis, we also provide tailored software development. This includes the building of AI powered tools such as predictive analytics technologies, chatbots, and intelligent solutions to enhance efficiency and decision making in a business."
            }
          />
          <Card_1
            title={"Compliance and Cybersecurity"}
            description={
              "There is no doubt that security is an important element in every digital solution. We also use advanced security measures including the most recent encryption techniques to ensure compliance standards while safeguarding your software from cyber threats. Your business operations are protected by our identity management regulatory compliance alongside data protection solutions."
            }
          />
        </div>
      </div>
    </div>
  );
};

export default Section3;
